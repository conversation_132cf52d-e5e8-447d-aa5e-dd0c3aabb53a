# Advanced Windows Sandbox Fix Script
# Run as Administrator in PowerShell

Write-Host "========================================" -ForegroundColor Green
Write-Host "Advanced Windows Sandbox Fix Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Function to check if running as admin
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Administrator)) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

Write-Host "Running as Administrator - OK" -ForegroundColor Green

# Fix 1: Registry repair for Container Manager Service
Write-Host "`n1. Fixing Container Manager Service registry..." -ForegroundColor Yellow
try {
    $regPath = "HKLM:\SYSTEM\CurrentControlSet\Services\CmService"
    if (Test-Path $regPath) {
        Set-ItemProperty -Path $regPath -Name "Start" -Value 3 -Force
        Write-Host "CmService registry fixed" -ForegroundColor Green
    } else {
        Write-Host "CmService registry path not found - may need Windows feature reinstall" -ForegroundColor Red
    }
} catch {
    Write-Host "Error fixing CmService registry: $($_.Exception.Message)" -ForegroundColor Red
}

# Fix 2: Reset Windows Sandbox completely
Write-Host "`n2. Complete Windows Sandbox reset..." -ForegroundColor Yellow
try {
    # Stop related services
    $services = @("CmService", "vmcompute")
    foreach ($service in $services) {
        $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
        if ($svc -and $svc.Status -eq "Running") {
            Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
            Write-Host "Stopped $service" -ForegroundColor Cyan
        }
    }
    
    # Disable and re-enable feature
    Write-Host "Disabling Windows Sandbox feature..." -ForegroundColor Cyan
    Disable-WindowsOptionalFeature -Online -FeatureName "Containers-DisposableClientVM" -NoRestart -ErrorAction SilentlyContinue
    
    Start-Sleep -Seconds 5
    
    Write-Host "Re-enabling Windows Sandbox feature..." -ForegroundColor Cyan
    Enable-WindowsOptionalFeature -Online -FeatureName "Containers-DisposableClientVM" -NoRestart -ErrorAction SilentlyContinue
    
    Write-Host "Windows Sandbox feature reset completed" -ForegroundColor Green
} catch {
    Write-Host "Error during feature reset: $($_.Exception.Message)" -ForegroundColor Red
}

# Fix 3: Hyper-V platform check and fix
Write-Host "`n3. Checking Hyper-V Platform..." -ForegroundColor Yellow
try {
    $hvPlatform = Get-WindowsOptionalFeature -Online -FeatureName "HypervisorPlatform" -ErrorAction SilentlyContinue
    if ($hvPlatform -and $hvPlatform.State -ne "Enabled") {
        Write-Host "Enabling Hyper-V Platform..." -ForegroundColor Cyan
        Enable-WindowsOptionalFeature -Online -FeatureName "HypervisorPlatform" -NoRestart
        Write-Host "Hyper-V Platform enabled" -ForegroundColor Green
    } else {
        Write-Host "Hyper-V Platform is already enabled or not available" -ForegroundColor Cyan
    }
} catch {
    Write-Host "Could not check/enable Hyper-V Platform" -ForegroundColor Red
}

# Fix 4: Container feature dependencies
Write-Host "`n4. Checking Container dependencies..." -ForegroundColor Yellow
try {
    $containerFeatures = @("Containers", "Microsoft-Hyper-V-Management-PowerShell")
    foreach ($feature in $containerFeatures) {
        $featureStatus = Get-WindowsOptionalFeature -Online -FeatureName $feature -ErrorAction SilentlyContinue
        if ($featureStatus -and $featureStatus.State -ne "Enabled") {
            Write-Host "Enabling $feature..." -ForegroundColor Cyan
            Enable-WindowsOptionalFeature -Online -FeatureName $feature -NoRestart -ErrorAction SilentlyContinue
        }
    }
} catch {
    Write-Host "Error checking container dependencies" -ForegroundColor Red
}

# Fix 5: Service startup repair
Write-Host "`n5. Repairing service startup..." -ForegroundColor Yellow
$criticalServices = @(
    @{Name="CmService"; StartType="Manual"},
    @{Name="vmcompute"; StartType="Manual"},
    @{Name="HvHost"; StartType="Manual"}
)

foreach ($svcInfo in $criticalServices) {
    try {
        $service = Get-Service -Name $svcInfo.Name -ErrorAction SilentlyContinue
        if ($service) {
            Set-Service -Name $svcInfo.Name -StartupType $svcInfo.StartType
            Write-Host "$($svcInfo.Name) startup type set to $($svcInfo.StartType)" -ForegroundColor Green
        }
    } catch {
        Write-Host "Could not configure $($svcInfo.Name)" -ForegroundColor Red
    }
}

# Fix 6: Windows Update components
Write-Host "`n6. Resetting Windows Update components..." -ForegroundColor Yellow
try {
    Stop-Service -Name "wuauserv" -Force -ErrorAction SilentlyContinue
    Stop-Service -Name "cryptSvc" -Force -ErrorAction SilentlyContinue
    Stop-Service -Name "bits" -Force -ErrorAction SilentlyContinue
    Stop-Service -Name "msiserver" -Force -ErrorAction SilentlyContinue
    
    Start-Service -Name "wuauserv" -ErrorAction SilentlyContinue
    Start-Service -Name "cryptSvc" -ErrorAction SilentlyContinue
    Start-Service -Name "bits" -ErrorAction SilentlyContinue
    
    Write-Host "Windows Update components reset" -ForegroundColor Green
} catch {
    Write-Host "Error resetting Windows Update components" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Advanced fix completed!" -ForegroundColor Green
Write-Host "IMPORTANT: Please restart your computer now." -ForegroundColor Yellow
Write-Host "After restart, try Windows Sandbox again." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

$restart = Read-Host "Do you want to restart now? (y/n)"
if ($restart -eq "y" -or $restart -eq "Y") {
    Write-Host "Restarting in 10 seconds..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    Restart-Computer -Force
}
