# Targeted Windows Sandbox Fix Script
# Based on diagnostic results - Run as Administrator

Write-Host "========================================" -ForegroundColor Green
Write-Host "Targeted Windows Sandbox Fix" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "Issues identified from diagnostic:" -ForegroundColor Yellow
Write-Host "1. BIOS Virtualization is DISABLED" -ForegroundColor Red
Write-Host "2. vmcompute.sys driver is MISSING" -ForegroundColor Red
Write-Host "3. HypervisorPlatform feature is DISABLED" -ForegroundColor Red
Write-Host ""

# Fix 1: Enable HypervisorPlatform
Write-Host "Fix 1: Enabling HypervisorPlatform feature..." -ForegroundColor Yellow
try {
    Enable-WindowsOptionalFeature -Online -FeatureName "HypervisorPlatform" -NoRestart
    Write-Host "✓ HypervisorPlatform enabled successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Error enabling HypervisorPlatform: $($_.Exception.Message)" -ForegroundColor Red
}

# Fix 2: Restore missing vmcompute.sys driver
Write-Host "`nFix 2: Restoring vmcompute.sys driver..." -ForegroundColor Yellow
try {
    # Try to restore from Windows component store
    DISM /Online /Cleanup-Image /RestoreHealth /Source:C:\Windows\WinSxS

    # Alternative: Copy from system32\DriverStore if available
    $driverStore = Get-ChildItem "C:\Windows\System32\DriverStore\FileRepository" -Recurse -Filter "vmcompute.sys" -ErrorAction SilentlyContinue
    if ($driverStore) {
        $sourcePath = $driverStore[0].FullName
        $destPath = "C:\Windows\System32\drivers\vmcompute.sys"
        Copy-Item -Path $sourcePath -Destination $destPath -Force
        Write-Host "✓ vmcompute.sys restored from driver store" -ForegroundColor Green
    } else {
        Write-Host "⚠ vmcompute.sys not found in driver store - will attempt system repair" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Error restoring vmcompute.sys: $($_.Exception.Message)" -ForegroundColor Red
}

# Fix 3: Reset Container features completely
Write-Host "`nFix 3: Resetting container features..." -ForegroundColor Yellow
try {
    # Disable all container-related features
    $features = @("Containers-DisposableClientVM", "Containers", "Microsoft-Hyper-V-All")
    foreach ($feature in $features) {
        Write-Host "Disabling $feature..." -ForegroundColor Cyan
        Disable-WindowsOptionalFeature -Online -FeatureName $feature -NoRestart -ErrorAction SilentlyContinue
    }
    
    Start-Sleep -Seconds 10
    
    # Re-enable in correct order
    Write-Host "Re-enabling features in correct order..." -ForegroundColor Cyan
    Enable-WindowsOptionalFeature -Online -FeatureName "HypervisorPlatform" -NoRestart -ErrorAction SilentlyContinue
    Enable-WindowsOptionalFeature -Online -FeatureName "Microsoft-Hyper-V-All" -NoRestart -ErrorAction SilentlyContinue
    Enable-WindowsOptionalFeature -Online -FeatureName "Containers" -NoRestart -ErrorAction SilentlyContinue
    Enable-WindowsOptionalFeature -Online -FeatureName "Containers-DisposableClientVM" -NoRestart -ErrorAction SilentlyContinue
    
    Write-Host "✓ Container features reset completed" -ForegroundColor Green
} catch {
    Write-Host "✗ Error resetting container features: $($_.Exception.Message)" -ForegroundColor Red
}

# Fix 4: System file repair for missing drivers
Write-Host "`nFix 4: Running system file repair..." -ForegroundColor Yellow
try {
    Write-Host "Running SFC scan..." -ForegroundColor Cyan
    $sfcResult = sfc /scannow
    Write-Host "SFC scan completed" -ForegroundColor Green
    
    Write-Host "Running DISM repair..." -ForegroundColor Cyan
    DISM /Online /Cleanup-Image /RestoreHealth
    Write-Host "DISM repair completed" -ForegroundColor Green
} catch {
    Write-Host "✗ Error during system file repair" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "SOFTWARE FIXES COMPLETED!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`n🔴 CRITICAL HARDWARE ISSUE DETECTED:" -ForegroundColor Red
Write-Host "BIOS Virtualization is DISABLED on your system!" -ForegroundColor Red
Write-Host ""
Write-Host "You MUST enable virtualization in BIOS for Windows Sandbox to work:" -ForegroundColor Yellow
Write-Host "1. Restart your computer" -ForegroundColor White
Write-Host "2. Press F2, F12, DEL, or ESC during boot to enter BIOS" -ForegroundColor White
Write-Host "3. Look for these settings:" -ForegroundColor White
Write-Host "   - Intel VT-x (for Intel CPUs)" -ForegroundColor Cyan
Write-Host "   - AMD-V (for AMD CPUs)" -ForegroundColor Cyan
Write-Host "   - Virtualization Technology" -ForegroundColor Cyan
Write-Host "   - Hardware Virtualization" -ForegroundColor Cyan
Write-Host "4. ENABLE the virtualization setting" -ForegroundColor White
Write-Host "5. Save and exit BIOS" -ForegroundColor White
Write-Host ""
Write-Host "After enabling BIOS virtualization, restart and try Windows Sandbox again." -ForegroundColor Yellow

$restart = Read-Host "`nDo you want to restart now to enter BIOS? (y/n)"
if ($restart -eq "y" -or $restart -eq "Y") {
    Write-Host "Restarting in 10 seconds..." -ForegroundColor Yellow
    Write-Host "Remember to press F2/F12/DEL during boot to enter BIOS!" -ForegroundColor Red
    Start-Sleep -Seconds 10
    Restart-Computer -Force
}
