# Windows Sandbox Feature Reset Script
# Run this as Administrator in PowerShell

Write-Host "========================================" -ForegroundColor Green
Write-Host "Windows Sandbox Feature Reset Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "Step 1: Checking current Windows Sandbox feature status..." -ForegroundColor Yellow
try {
    $feature = Get-WindowsOptionalFeature -Online -FeatureName "Containers-DisposableClientVM"
    Write-Host "Current status: $($feature.State)" -ForegroundColor Cyan
} catch {
    Write-Host "Could not check feature status. Proceeding with reset..." -ForegroundColor Red
}

Write-Host ""
Write-Host "Step 2: Disabling Windows Sandbox feature..." -ForegroundColor Yellow
try {
    Disable-WindowsOptionalFeature -Online -FeatureName "Containers-DisposableClientVM" -NoRestart
    Write-Host "Windows Sandbox feature disabled successfully." -ForegroundColor Green
} catch {
    Write-Host "Error disabling feature: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Step 3: Re-enabling Windows Sandbox feature..." -ForegroundColor Yellow
try {
    Enable-WindowsOptionalFeature -Online -FeatureName "Containers-DisposableClientVM" -NoRestart
    Write-Host "Windows Sandbox feature enabled successfully." -ForegroundColor Green
} catch {
    Write-Host "Error enabling feature: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Step 4: Checking Hyper-V services..." -ForegroundColor Yellow
$services = @("HvHost", "vmms")
foreach ($service in $services) {
    $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
    if ($svc) {
        Write-Host "$service service status: $($svc.Status)" -ForegroundColor Cyan
        if ($svc.Status -ne "Running") {
            try {
                Start-Service -Name $service
                Write-Host "$service service started." -ForegroundColor Green
            } catch {
                Write-Host "Could not start $service service: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Feature reset completed!" -ForegroundColor Green
Write-Host "Please restart your computer and try Windows Sandbox again." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Read-Host "Press Enter to exit"
