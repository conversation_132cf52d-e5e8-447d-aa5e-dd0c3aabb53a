# Advanced Windows Sandbox Diagnostic Script
# Run as Administrator in PowerShell

Write-Host "========================================" -ForegroundColor Green
Write-Host "Advanced Windows Sandbox Diagnostic" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check Windows version and edition
Write-Host "`n1. System Information:" -ForegroundColor Yellow
$os = Get-CimInstance Win32_OperatingSystem
Write-Host "OS: $($os.Caption)" -ForegroundColor Cyan
Write-Host "Version: $($os.Version)" -ForegroundColor Cyan
Write-Host "Build: $($os.BuildNumber)" -ForegroundColor Cyan

# Check if Pro/Enterprise/Education edition
if ($os.Caption -notmatch "Pro|Enterprise|Education") {
    Write-Host "WARNING: Windows Sandbox requires Pro, Enterprise, or Education edition!" -ForegroundColor Red
}

# Check virtualization support
Write-Host "`n2. Virtualization Support:" -ForegroundColor Yellow
try {
    $hyperv = systeminfo | Select-String "Hyper-V Requirements"
    Write-Host $hyperv -ForegroundColor Cyan
} catch {
    Write-Host "Could not check Hyper-V requirements" -ForegroundColor Red
}

# Check BIOS virtualization
$cpu = Get-CimInstance Win32_Processor
Write-Host "CPU: $($cpu.Name)" -ForegroundColor Cyan
Write-Host "Virtualization Firmware Enabled: $($cpu.VirtualizationFirmwareEnabled)" -ForegroundColor Cyan

# Check Windows Features
Write-Host "`n3. Windows Features Status:" -ForegroundColor Yellow
try {
    $features = @(
        "Containers-DisposableClientVM",
        "Microsoft-Hyper-V-All",
        "Microsoft-Hyper-V",
        "HypervisorPlatform"
    )
    
    foreach ($feature in $features) {
        try {
            $status = Get-WindowsOptionalFeature -Online -FeatureName $feature -ErrorAction SilentlyContinue
            if ($status) {
                Write-Host "$feature : $($status.State)" -ForegroundColor Cyan
            } else {
                Write-Host "$feature : Not Available" -ForegroundColor Red
            }
        } catch {
            Write-Host "$feature : Error checking" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Error checking Windows features" -ForegroundColor Red
}

# Check critical services
Write-Host "`n4. Critical Services:" -ForegroundColor Yellow
$services = @("HvHost", "vmms", "VmCompute", "CmService")
foreach ($service in $services) {
    $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
    if ($svc) {
        $color = if ($svc.Status -eq "Running") { "Green" } else { "Red" }
        Write-Host "$service : $($svc.Status)" -ForegroundColor $color
    } else {
        Write-Host "$service : Not Found" -ForegroundColor Red
    }
}

# Check registry keys
Write-Host "`n5. Registry Check:" -ForegroundColor Yellow
$regPaths = @(
    "HKLM:\SYSTEM\CurrentControlSet\Services\CmService",
    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsSandbox"
)

foreach ($path in $regPaths) {
    if (Test-Path $path) {
        Write-Host "$path : Exists" -ForegroundColor Green
        try {
            $startValue = Get-ItemProperty -Path $path -Name "Start" -ErrorAction SilentlyContinue
            if ($startValue) {
                Write-Host "  Start value: $($startValue.Start)" -ForegroundColor Cyan
            }
        } catch {}
    } else {
        Write-Host "$path : Missing" -ForegroundColor Red
    }
}

# Check file integrity
Write-Host "`n6. File Integrity:" -ForegroundColor Yellow
$files = @(
    "C:\Windows\System32\WindowsSandbox.exe",
    "C:\Windows\System32\vmcompute.exe",
    "C:\Windows\System32\drivers\vmcompute.sys"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        $fileInfo = Get-Item $file
        Write-Host "$file : Exists ($($fileInfo.Length) bytes)" -ForegroundColor Green
    } else {
        Write-Host "$file : Missing" -ForegroundColor Red
    }
}

# Check Event Logs for errors
Write-Host "`n7. Recent Error Events:" -ForegroundColor Yellow
try {
    $events = Get-WinEvent -FilterHashtable @{LogName='System'; Level=2; StartTime=(Get-Date).AddDays(-1)} -MaxEvents 5 -ErrorAction SilentlyContinue | 
              Where-Object {$_.Message -match "sandbox|hyper|vm|container"}
    
    if ($events) {
        foreach ($event in $events) {
            Write-Host "Event ID $($event.Id): $($event.LevelDisplayName)" -ForegroundColor Red
            Write-Host "  $($event.Message.Substring(0, [Math]::Min(100, $event.Message.Length)))..." -ForegroundColor Gray
        }
    } else {
        Write-Host "No recent relevant error events found" -ForegroundColor Green
    }
} catch {
    Write-Host "Could not check event logs" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Diagnostic completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Read-Host "Press Enter to exit"
