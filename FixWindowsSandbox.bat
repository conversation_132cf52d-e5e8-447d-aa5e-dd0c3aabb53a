@echo off
echo ========================================
echo Windows Sandbox Repair Script
echo ========================================
echo.

echo Step 1: Starting required services...
net start BITS
net start Winmgmt
echo.

echo Step 2: Running System File Checker...
echo This may take 10-15 minutes, please wait...
sfc /scannow
echo.

echo Step 3: Running DISM Image Repair...
echo This may take 15-20 minutes, please wait...
DISM /Online /Cleanup-Image /RestoreHealth
echo.

echo Step 4: Checking Windows Sandbox feature status...
dism /online /get-features | findstr /i "containers-disposableclientvm"
echo.

echo Step 5: Testing Windows Sandbox executable...
if exist "C:\Windows\System32\WindowsSandbox.exe" (
    echo Windows Sandbox executable found.
) else (
    echo ERROR: Windows Sandbox executable not found!
)
echo.

echo ========================================
echo Repair process completed!
echo Please restart your computer and try Windows Sandbox again.
echo ========================================
pause
