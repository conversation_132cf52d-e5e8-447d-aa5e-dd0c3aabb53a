# Simple Windows Sandbox Fix Script
# Based on diagnostic results - Run as Administrator

Write-Host "========================================" -ForegroundColor Green
Write-Host "Windows Sandbox Fix Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "Issues identified:" -ForegroundColor Yellow
Write-Host "1. BIOS Virtualization is DISABLED" -ForegroundColor Red
Write-Host "2. vmcompute.sys driver is MISSING" -ForegroundColor Red
Write-Host "3. HypervisorPlatform feature is DISABLED" -ForegroundColor Red
Write-Host ""

# Fix 1: Enable HypervisorPlatform
Write-Host "Fix 1: Enabling HypervisorPlatform feature..." -ForegroundColor Yellow
Enable-WindowsOptionalFeature -Online -FeatureName "HypervisorPlatform" -NoRestart
Write-Host "HypervisorPlatform enabled" -ForegroundColor Green

# Fix 2: System file repair
Write-Host "`nFix 2: Running system file repair..." -ForegroundColor Yellow
Write-Host "Running SFC scan (this may take 10-15 minutes)..." -ForegroundColor Cyan
sfc /scannow
Write-Host "Running DISM repair..." -ForegroundColor Cyan
DISM /Online /Cleanup-Image /RestoreHealth
Write-Host "System repair completed" -ForegroundColor Green

# Fix 3: Reset Windows Sandbox feature
Write-Host "`nFix 3: Resetting Windows Sandbox feature..." -ForegroundColor Yellow
Write-Host "Disabling Windows Sandbox..." -ForegroundColor Cyan
Disable-WindowsOptionalFeature -Online -FeatureName "Containers-DisposableClientVM" -NoRestart

Write-Host "Waiting 10 seconds..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

Write-Host "Re-enabling Windows Sandbox..." -ForegroundColor Cyan
Enable-WindowsOptionalFeature -Online -FeatureName "Containers-DisposableClientVM" -NoRestart
Write-Host "Windows Sandbox feature reset completed" -ForegroundColor Green

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "SOFTWARE FIXES COMPLETED!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`nCRITICAL: BIOS Virtualization is DISABLED!" -ForegroundColor Red
Write-Host "You MUST enable virtualization in BIOS:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Restart your computer" -ForegroundColor White
Write-Host "2. Press F2, F12, DEL, or ESC during boot to enter BIOS" -ForegroundColor White
Write-Host "3. Look for 'Intel VT-x' or 'Virtualization Technology'" -ForegroundColor White
Write-Host "4. ENABLE the virtualization setting" -ForegroundColor White
Write-Host "5. Save and exit BIOS (usually F10)" -ForegroundColor White
Write-Host ""
Write-Host "After enabling BIOS virtualization, Windows Sandbox should work!" -ForegroundColor Yellow

Write-Host "`nPress Enter to continue..."
Read-Host
